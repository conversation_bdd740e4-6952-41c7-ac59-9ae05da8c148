package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsFeeRuleEntity;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleDetailVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleSaveVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleUpdateVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleVersionVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleVersionCompareVo;
import com.jygjexp.jynx.tms.vo.TmsSurchargeRuleVo;
import com.jygjexp.jynx.tms.vo.excel.TmsFeeRuleExcelVo;

import java.util.List;

public interface TmsFeeRuleService extends IService<TmsFeeRuleEntity> {

    // 附加费分页查询
    Page<TmsFeeRuleEntity> search(Page page, TmsFeeRuleEntity tmsFeeRule);

    // 新增附加费
    boolean saveFeeRule(TmsFeeRuleSaveVo tmsFeeRule);

    // 修改附加费
    boolean updateFeeRule(TmsFeeRuleUpdateVo tmsFeeRule);

    // 根据ID查询附加费详情
    TmsFeeRuleDetailVo getFeeRuleDetailById(Long id);

    // 根据ID批量查询附加费详情
    List<TmsFeeRuleDetailVo> getBatchByIds(List<Long> ids);

    // 创建新版本（替代原有的修改方法）
    boolean createNewVersion(TmsFeeRuleUpdateVo tmsFeeRule, String versionDescription);

    // 查询指定费用规则的所有版本
    List<TmsFeeRuleVersionVo> getFeeRuleVersions(Long originalFeeRuleId);

    // 获取当前生效版本
    TmsFeeRuleDetailVo getCurrentVersion(Long originalFeeRuleId);

    // 版本对比
    TmsFeeRuleVersionCompareVo compareVersions(Long oldVersionId, Long newVersionId);

    // 激活指定版本
    boolean activateVersion(Long versionId);

    // 根据原始费用规则ID删除所有版本
    R deleteByOriginalFeeRuleIds(Long[] originalFeeRuleIds);

    // 附加费导出
    List<TmsFeeRuleExcelVo> feeRuleExport(TmsFeeRuleEntity tmsFeeRule, Long[] ids);

    // 根据服务商ID查询有效的附加费规则
    List<TmsFeeRuleEntity> getValidFeeRulesByProviderId(Long providerId);

}