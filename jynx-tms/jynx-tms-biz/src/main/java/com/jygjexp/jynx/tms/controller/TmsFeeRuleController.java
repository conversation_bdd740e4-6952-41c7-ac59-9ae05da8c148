package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsFeeRuleEntity;
import com.jygjexp.jynx.tms.entity.TmsFeeRuleExprEntity;
import com.jygjexp.jynx.tms.service.TmsFeeRuleExprService;
import com.jygjexp.jynx.tms.service.TmsFeeRuleService;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleDetailVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleSaveVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleUpdateVo;
import com.jygjexp.jynx.tms.vo.TmsSurchargeRuleVo;
import com.jygjexp.jynx.tms.vo.excel.TmsFeeRuleExcelVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 附加费
 *
 * <AUTHOR>
 * @date 2025-07-14 15:12:06
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsFeeRule" )
@Tag(description = "tmsFeeRule" , name = "附加费管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsFeeRuleController {

    private final  TmsFeeRuleService tmsFeeRuleService;
    private final TmsFeeRuleExprService tmsFeeRuleExprService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsFeeRule 附加费
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_view')" )
    public R getTmsFeeRulePage(@ParameterObject Page page, @ParameterObject TmsFeeRuleEntity tmsFeeRule) {
        return R.ok(tmsFeeRuleService.search(page, tmsFeeRule));
    }


    /**
     * 通过id查询附加费
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        if (id == null) {
            return R.failed("ID不能为空");
        }

        TmsFeeRuleDetailVo detailVo = tmsFeeRuleService.getFeeRuleDetailById(id);
        if (detailVo == null) {
            return R.failed("未找到对应的附加费信息");
        }

        return R.ok(detailVo);
    }

    /**
     * 通过ids批量查询附加费
     * @param ids ids
     * @return R
     */
    @Operation(summary = "通过ids批量查询附加费" , description = "通过ids批量查询附加费" )
    @PostMapping("/batchGetByIds" )
    public R getBatchByIds(@RequestBody List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return R.failed("ID不能为空");
        }

        List<TmsFeeRuleDetailVo> detailVo = tmsFeeRuleService.getBatchByIds(ids);
        if (detailVo == null) {
            return R.failed("未找到对应的附加费信息");
        }

        return R.ok(detailVo);
    }

    /**
     * 新增附加费
     * @param tmsFeeRule 附加费
     * @return R
     */
    @Operation(summary = "新增附加费" , description = "新增附加费" )
    @SysLog("新增附加费" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_add')" )
    public R save(@RequestBody TmsFeeRuleSaveVo tmsFeeRule) {
        return R.ok(tmsFeeRuleService.saveFeeRule(tmsFeeRule));
    }

    /**
     * 修改附加费
     * @param tmsFeeRule 附加费
     * @return R
     */
    @Operation(summary = "修改附加费" , description = "修改附加费" )
    @SysLog("修改附加费" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_edit')" )
    public R updateById(@RequestBody TmsFeeRuleUpdateVo tmsFeeRule) {
        return R.ok(tmsFeeRuleService.updateFeeRule(tmsFeeRule));
    }

    /**
     * 编辑启用停用
     * @param tmsFeeRule 附加费
     * @return R
     */
    @Operation(summary = "编辑启用停用" , description = "编辑启用停用" )
    @PutMapping("/updateIsValidById")
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_isValid')" )
    public R updateIsValidById(@RequestBody TmsFeeRuleEntity tmsFeeRule) {
        return R.ok(tmsFeeRuleService.updateById(tmsFeeRule));
    }

    /**
     * 根据原始费用规则ID删除所有版本
     * @param originalFeeRuleIds 原始费用规则ID列表
     * @return R 包含删除统计信息
     */
    @Operation(summary = "根据原始费用规则ID删除所有版本", description = "删除指定费用规则的所有历史版本和关联数据")
    @SysLog("根据原始费用规则ID删除所有版本")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_del')")
    public R deleteByOriginalFeeRuleIds(@RequestBody Long[] originalFeeRuleIds) {
            if (originalFeeRuleIds == null || originalFeeRuleIds.length == 0) {
                return R.failed("原始费用规则ID不能为空");
            }
            return tmsFeeRuleService.deleteByOriginalFeeRuleIds(originalFeeRuleIds);
    }


    /**
     * 导出excel 表格
     * @param tmsFeeRule 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsFeeRule_export')" )
    public List<TmsFeeRuleExcelVo> export(TmsFeeRuleEntity tmsFeeRule, Long[] ids) {
        return tmsFeeRuleService.feeRuleExport(tmsFeeRule,ids);
    }
}