package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsFeeRuleEntity;
import com.jygjexp.jynx.tms.entity.TmsFeeRuleExprEntity;
import com.jygjexp.jynx.tms.mapper.TmsFeeRuleExprMapper;
import com.jygjexp.jynx.tms.mapper.TmsFeeRuleMapper;
import com.jygjexp.jynx.tms.service.TmsFeeRuleService;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleDetailVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleSaveVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleUpdateVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleVersionVo;
import com.jygjexp.jynx.tms.vo.TmsFeeRuleVersionCompareVo;
import com.jygjexp.jynx.tms.vo.TmsSurchargeRuleVo;
import com.jygjexp.jynx.tms.vo.excel.TmsFeeRuleExcelVo;
import com.jygjexp.jynx.tms.utils.SnowflakeIdGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 附加费
 *
 * <AUTHOR>
 * @date 2025-07-14 15:12:06
 */
@RequiredArgsConstructor
@Service
public class TmsFeeRuleServiceImpl extends ServiceImpl<TmsFeeRuleMapper, TmsFeeRuleEntity> implements TmsFeeRuleService {

    private final TmsFeeRuleMapper tmsFeeRuleMapper;
    private final TmsFeeRuleExprMapper tmsFeeRuleExprMapper;
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    // 附加费分页查询（只查询当前生效版本）
    @Override
    public Page<TmsFeeRuleEntity> search(Page page, TmsFeeRuleEntity tmsFeeRule) {
        LambdaQueryWrapper<TmsFeeRuleEntity> wrapper = new LambdaQueryWrapper<>();

        // 基础查询条件
        wrapper.like(StrUtil.isNotBlank(tmsFeeRule.getFeeCode()),TmsFeeRuleEntity::getFeeCode, tmsFeeRule.getFeeCode());
        wrapper.like(StrUtil.isNotBlank(tmsFeeRule.getFeeName()),TmsFeeRuleEntity::getFeeName, tmsFeeRule.getFeeName());
        wrapper.eq(ObjUtil.isNotNull(tmsFeeRule.getIsValid()),TmsFeeRuleEntity::getIsValid, tmsFeeRule.getIsValid());

        // 只查询当前生效版本
        wrapper.eq(TmsFeeRuleEntity::getIsActive, 1);
        // 排序：优先按创建时间降序，确保最新版本在前
        wrapper.orderByDesc(TmsFeeRuleEntity::getCreateTime);

        return this.page(page, wrapper);
    }

    // 新增附加费规则
    @Override
    public boolean saveFeeRule(TmsFeeRuleSaveVo tmsFeeRule) {
        TmsFeeRuleEntity tmsFeeRuleEntity = tmsFeeRule.getFeeRule();
        List<TmsSurchargeRuleVo> rules = tmsFeeRule.getRules();

        if (tmsFeeRuleEntity == null) {
            return false;
        }

        // 检查并自动生成费用代码
        if (StrUtil.isBlank(tmsFeeRuleEntity.getFeeCode())) {
            String generatedFeeCode = generateFeeCode();
            tmsFeeRuleEntity.setFeeCode(generatedFeeCode);
        }
        // 新增附加费信息
        int saveFee = tmsFeeRuleMapper.insert(tmsFeeRuleEntity);

        if (saveFee > 0) {
            // 设置原始费用规则ID（首次创建时，原始ID就是自己的ID）
            tmsFeeRuleEntity.setOriginalFeeRuleId(tmsFeeRuleEntity.getId());
            tmsFeeRuleMapper.updateById(tmsFeeRuleEntity);
            // 新增附加费表达式规则
            if (null != rules && !rules.isEmpty()) {
                // 拼接表达式
                String fullExpression = buildFullExpression(rules);
                TmsFeeRuleExprEntity tmsFeeRuleExpr = new TmsFeeRuleExprEntity();
                tmsFeeRuleExpr.setFeeId(tmsFeeRuleEntity.getId());
                tmsFeeRuleExpr.setExpression(fullExpression);
                return tmsFeeRuleExprMapper.insert(tmsFeeRuleExpr) > 0;
            }
            return true;
        }
        return false;
    }

    // 修改附加费规则
    @Override
    @Deprecated
    public boolean updateFeeRule(TmsFeeRuleUpdateVo tmsFeeRule) {
        // 调用新的版本创建方法
        return createNewVersion(tmsFeeRule, "系统自动版本更新");
    }

    // 创建新版本（替代原有的修改方法）
    @Override
    public boolean createNewVersion(TmsFeeRuleUpdateVo tmsFeeRule, String versionDescription) {
        TmsFeeRuleEntity tmsFeeRuleEntity = tmsFeeRule.getFeeRule();
        List<TmsSurchargeRuleVo> rules = tmsFeeRule.getRules();

        if (tmsFeeRuleEntity == null || tmsFeeRuleEntity.getId() == null) {
            return false;
        }

        // 验证当前版本是否存在
        TmsFeeRuleEntity currentVersion = tmsFeeRuleMapper.selectById(tmsFeeRuleEntity.getId());
        if (currentVersion == null) {
            return false;
        }

        // 获取原始费用规则ID
        Long originalFeeRuleId = currentVersion.getOriginalFeeRuleId();
        if (originalFeeRuleId == null) {
            originalFeeRuleId = currentVersion.getId();
        }

        // 获取下一个版本号
        Integer nextVersion = getNextVersionNumber(originalFeeRuleId);

        // 将当前所有版本设置为非活跃状态
        deactivateAllVersions(originalFeeRuleId);

        // 创建新版本记录
        TmsFeeRuleEntity newVersionEntity = new TmsFeeRuleEntity();
        // 复制所有字段
        BeanUtils.copyProperties(tmsFeeRuleEntity, newVersionEntity);

        // 设置版本相关字段
        newVersionEntity.setId(null);
        newVersionEntity.setVersion(nextVersion);
        newVersionEntity.setOriginalFeeRuleId(originalFeeRuleId);
        newVersionEntity.setIsActive(1); // 新版本为当前活跃版本

        // 保存新版本
        int saveResult = tmsFeeRuleMapper.insert(newVersionEntity);
        if (saveResult > 0) {
            // 处理表达式规则
            if (rules != null && !rules.isEmpty()) {
                String fullExpression = buildFullExpression(rules);
                TmsFeeRuleExprEntity tmsFeeRuleExpr = new TmsFeeRuleExprEntity();
                tmsFeeRuleExpr.setFeeId(newVersionEntity.getId());
                tmsFeeRuleExpr.setExpression(fullExpression);
                return tmsFeeRuleExprMapper.insert(tmsFeeRuleExpr) > 0;
            }
            return true;
        }
        return false;
    }

    // 根据ID查询附加费详情
    @Override
    public TmsFeeRuleDetailVo getFeeRuleDetailById(Long id) {
        // 查询附加费基本信息
        TmsFeeRuleEntity feeRule = tmsFeeRuleMapper.selectById(id);
        if (feeRule == null) {
            return null;
        }

        // 查询关联的表达式规则
        LambdaQueryWrapper<TmsFeeRuleExprEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsFeeRuleExprEntity::getFeeId, id);
        TmsFeeRuleExprEntity feeRuleExpr = tmsFeeRuleExprMapper.selectOne(wrapper);

        // 构建返回对象
        TmsFeeRuleDetailVo detailVo = new TmsFeeRuleDetailVo();
        detailVo.setFeeRule(feeRule);

        // 如果存在表达式，解析为规则列表
        if (feeRuleExpr != null && StrUtil.isNotBlank(feeRuleExpr.getExpression())) {
            List<TmsSurchargeRuleVo> rules = parseExpressionToRules(feeRuleExpr.getExpression());
            detailVo.setRules(rules);
        }

        return detailVo;
    }

    /**
     * 通过ids批量查询附加费详情
     */
    @Override
    public List<TmsFeeRuleDetailVo> getBatchByIds(List<Long> ids) {
        // 一次性查出所有附加费基本信息
        List<TmsFeeRuleEntity> feeRules = tmsFeeRuleMapper.selectBatchIds(ids);
        if (feeRules == null || feeRules.isEmpty()) {
            return Collections.emptyList();
        }

        // 一次性查出所有表达式规则
        LambdaQueryWrapper<TmsFeeRuleExprEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TmsFeeRuleExprEntity::getFeeId, ids);
        List<TmsFeeRuleExprEntity> feeRuleExprList = tmsFeeRuleExprMapper.selectList(wrapper);

        // 以 feeId 为 key 建立映射
        Map<Long, TmsFeeRuleExprEntity> exprMap = feeRuleExprList.stream()
                .collect(Collectors.toMap(TmsFeeRuleExprEntity::getFeeId, e -> e, (e1, e2) -> e1));

        // 构建返回对象列表
        List<TmsFeeRuleDetailVo> result = new ArrayList<>();
        for (TmsFeeRuleEntity feeRule : feeRules) {
            TmsFeeRuleDetailVo detailVo = new TmsFeeRuleDetailVo();
            detailVo.setFeeRule(feeRule);

            TmsFeeRuleExprEntity exprEntity = exprMap.get(feeRule.getId());
            if (exprEntity != null && StrUtil.isNotBlank(exprEntity.getExpression())) {
                List<TmsSurchargeRuleVo> rules = parseExpressionToRules(exprEntity.getExpression());
                detailVo.setRules(rules);
            }

            result.add(detailVo);
        }

        return result;
    }


    // 拼接表达式
    public String buildFullExpression(List<TmsSurchargeRuleVo> rules) {
        StringBuilder expression = new StringBuilder();

        for (int i = 0; i < rules.size(); i++) {
            TmsSurchargeRuleVo rule = rules.get(i);
            String var = rule.getVariableName(); // 后续扩展可以后端手动加上 #

            // 拼接条件：变量在左边，值在右边
            String leftExpr = String.format("%s %s %s",
                    var, rule.getLeftOperator(), rule.getLeftValue().stripTrailingZeros().toPlainString());

            String rightExpr = String.format("%s %s %s",
                    var, rule.getRightOperator(), rule.getRightValue().stripTrailingZeros().toPlainString());

            // 拼接连接符
            if (i > 0 && StringUtils.hasText(rule.getConnector())) {
                expression.append(" ").append(rule.getConnector()).append(" ");
            }

            expression.append("(").append(leftExpr).append(" && ").append(rightExpr).append(")");
        }

        return expression.toString();
    }

    /**
     * 根据原始费用规则ID删除所有版本
     *
     * @param originalFeeRuleIds 原始费用规则ID数组
     * @return 删除结果统计
     */
    @Override
    public R deleteByOriginalFeeRuleIds(Long[] originalFeeRuleIds) {
        if (originalFeeRuleIds == null || originalFeeRuleIds.length == 0) {
            return R.failed("原始费用规则ID不能为空");
        }

        List<Long> allFeeRuleIds = new ArrayList<>();
        List<Long> processedOriginalIds = new ArrayList<>();

        // 查询所有需要删除的费用规则版本
        for (Long originalFeeRuleId : originalFeeRuleIds) {
            if (originalFeeRuleId == null) {
                continue;
            }
            LambdaQueryWrapper<TmsFeeRuleEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TmsFeeRuleEntity::getOriginalFeeRuleId, originalFeeRuleId);
            List<TmsFeeRuleEntity> versions = tmsFeeRuleMapper.selectList(wrapper);

            if (!versions.isEmpty()) {
                List<Long> versionIds = versions.stream()
                        .map(TmsFeeRuleEntity::getId)
                        .collect(Collectors.toList());
                allFeeRuleIds.addAll(versionIds);
                processedOriginalIds.add(originalFeeRuleId);
            }
        }

        // 删除费用规则表达式
        if (!allFeeRuleIds.isEmpty()) {
            LambdaQueryWrapper<TmsFeeRuleExprEntity> exprWrapper = new LambdaQueryWrapper<>();
            exprWrapper.in(TmsFeeRuleExprEntity::getFeeId, allFeeRuleIds);
            tmsFeeRuleExprMapper.delete(exprWrapper);
        }

        // 删除费用规则
        int deletedFeeRuleCount = 0;
        if (!allFeeRuleIds.isEmpty()) {
            deletedFeeRuleCount = tmsFeeRuleMapper.deleteBatchIds(allFeeRuleIds);
        }

        return R.ok(deletedFeeRuleCount>0);
    }

    // 导出（只导出当前生效版本）
    @Override
    public List<TmsFeeRuleExcelVo> feeRuleExport(TmsFeeRuleEntity tmsFeeRule, Long[] ids) {
        MPJLambdaWrapper<TmsFeeRuleEntity> wrapper = new MPJLambdaWrapper<>();

        // 基础查询条件
        wrapper.like(StrUtil.isNotBlank(tmsFeeRule.getFeeCode()),TmsFeeRuleEntity::getFeeCode, tmsFeeRule.getFeeCode());
        wrapper.like(StrUtil.isNotBlank(tmsFeeRule.getFeeName()),TmsFeeRuleEntity::getFeeName, tmsFeeRule.getFeeName());
        wrapper.eq(ObjUtil.isNotNull(tmsFeeRule.getIsValid()),TmsFeeRuleEntity::getIsValid, tmsFeeRule.getIsValid());
        wrapper.in(ObjUtil.isNotNull(ids) && ids.length > 0, TmsFeeRuleEntity::getId, ids);

        // 关键：只导出当前生效版本
        wrapper.eq(TmsFeeRuleEntity::getIsActive, 1);

        wrapper.orderByDesc(TmsFeeRuleEntity::getCreateTime);
        return tmsFeeRuleMapper.selectJoinList(TmsFeeRuleExcelVo.class,wrapper);
    }

    /**
     * 自动生成费用代码
     * 生成规则：FEE + 年月日 + Snowflake ID后6位
     * 格式示例：FEE250715123456
     *
     * @return 生成的费用代码
     */
    private String generateFeeCode() {
        // 前缀
        String prefix = "FEE";

        // 日期部分（年月日，如：250715）
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));

        // 使用Snowflake ID生成唯一序号（取后6位）
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 6);

        // 组合费用代码
        String feeCode = prefix + datePart + shortId;

        // 检查代码唯一性，如果重复则重新生成
        while (isFeeCodeExists(feeCode)) {
            snowflakeId = snowflakeIdGenerator.nextId();
            snowflakeIdStr = String.valueOf(snowflakeId);
            shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 6);
            feeCode = prefix + datePart + shortId;
        }

        return feeCode;
    }

    /**
     * 检查费用代码是否已存在
     *
     * @param feeCode 费用代码
     * @return true-已存在，false-不存在
     */
    private boolean isFeeCodeExists(String feeCode) {
        LambdaQueryWrapper<TmsFeeRuleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsFeeRuleEntity::getFeeCode, feeCode);
        return tmsFeeRuleMapper.selectCount(wrapper) > 0;
    }

    /**
     * 解析表达式字符串为规则列表
     * 表达式格式：(30 >= weight && weight < 60) && (10 >= volume && volume < 20)
     *
     * @param expression 表达式字符串
     * @return 解析后的规则列表
     */
    private List<TmsSurchargeRuleVo> parseExpressionToRules(String expression) {
        List<TmsSurchargeRuleVo> rules = new ArrayList<>();
        if (StrUtil.isBlank(expression)) {
            return rules;
        }

        try {
            List<String> ruleParts = extractRuleParts(expression);
            List<String> connectors = extractConnectors(expression);

            // 支持任意表达式变量（.+? 是非贪婪匹配）
            String rulePattern = "\\((.+?)\\s*(>=|>|<=|<|==|!=)\\s*([\\d.]+)\\s*&&\\s*(.+?)\\s*(>=|>|<=|<|==|!=)\\s*([\\d.]+)\\)";
            Pattern pattern = Pattern.compile(rulePattern);

            for (int i = 0; i < ruleParts.size(); i++) {
                String rulePart = ruleParts.get(i).trim();
                Matcher matcher = pattern.matcher(rulePart);

                if (matcher.find()) {
                    String expr1 = matcher.group(1).trim(); // 表达式1
                    String op1 = matcher.group(2);
                    String val1 = matcher.group(3);
                    String expr2 = matcher.group(4).trim(); // 表达式2
                    String op2 = matcher.group(5);
                    String val2 = matcher.group(6);

                    // 表达式文本必须一致（否则两边是不同表达式）
                    if (!expr1.equals(expr2)) {
                        System.err.println("两侧表达式不一致，跳过：" + rulePart);
                        continue;
                    }

                    TmsSurchargeRuleVo rule = new TmsSurchargeRuleVo();
                    rule.setVariableName(expr1); // 保留完整表达式（如 #length+(#width+#height)*2）
                    rule.setLeftOperator(op1);
                    rule.setLeftValue(new BigDecimal(val1));
                    rule.setRightOperator(op2);
                    rule.setRightValue(new BigDecimal(val2));
                    rule.setExpression(rulePart);

                    if (i > 0 && i - 1 < connectors.size()) {
                        rule.setConnector(connectors.get(i - 1));
                    }

                    rules.add(rule);
                } else {
                    System.err.println("规则匹配失败: " + rulePart);
                }
            }
        } catch (Exception e) {
            System.err.println("表达式解析异常: " + expression + ", 错误: " + e.getMessage());
            e.printStackTrace();
            rules.clear();
        }

        return rules;
    }





    /**
     * 提取表达式中的规则部分（括号内的内容）
     */
    private List<String> extractRuleParts(String expression) {
        List<String> parts = new ArrayList<>();
        int depth = 0;
        int start = -1;

        for (int i = 0; i < expression.length(); i++) {
            char ch = expression.charAt(i);

            if (ch == '(') {
                if (depth == 0) {
                    start = i;
                }
                depth++;
            } else if (ch == ')') {
                depth--;
                if (depth == 0 && start != -1) {
                    parts.add(expression.substring(start, i + 1));
                    start = -1;
                }
            }
        }

        return parts;
    }


    /**
     * 提取表达式中的连接符
     */
    private List<String> extractConnectors(String expression) {
        List<String> connectors = new ArrayList<>();

        // 正则提取所有连接符（匹配 && 或 ||），前后必须是闭括号和开括号之间的连接
        Pattern pattern = Pattern.compile("\\)\\s*(&&|\\|\\|)\\s*\\(");
        Matcher matcher = pattern.matcher(expression);

        while (matcher.find()) {
            connectors.add(matcher.group(1));
        }

        return connectors;
    }

    /**
     * 从完整表达式中提取连接符
     *
     * @param fullExpression 完整表达式
     * @param currentRule 当前规则部分
     * @return 连接符（&& 或 ||）
     */
    private String extractConnector(String fullExpression, String currentRule) {
        int ruleIndex = fullExpression.indexOf(currentRule);
        if (ruleIndex != -1) {
            int endIndex = ruleIndex + currentRule.length();
            if (endIndex < fullExpression.length()) {
                String remaining = fullExpression.substring(endIndex).trim();
                if (remaining.startsWith("&&")) {
                    return "&&";
                } else if (remaining.startsWith("||")) {
                    return "||";
                }
            }
        }
        return "&&"; // 默认返回 &&
    }

    // ==================== 版本管理相关方法 ====================

    /**
     * 查询指定费用规则的所有版本
     */
    @Override
    public List<TmsFeeRuleVersionVo> getFeeRuleVersions(Long originalFeeRuleId) {
        if (originalFeeRuleId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<TmsFeeRuleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsFeeRuleEntity::getOriginalFeeRuleId, originalFeeRuleId)
                .orderByDesc(TmsFeeRuleEntity::getVersion);

        List<TmsFeeRuleEntity> feeRules = tmsFeeRuleMapper.selectList(wrapper);
        List<TmsFeeRuleVersionVo> versions = new ArrayList<>();

        for (TmsFeeRuleEntity feeRule : feeRules) {
            TmsFeeRuleVersionVo versionVo = new TmsFeeRuleVersionVo();
            versionVo.setId(feeRule.getId());
            versionVo.setOriginalFeeRuleId(feeRule.getOriginalFeeRuleId());
            versionVo.setFeeCode(feeRule.getFeeCode());
            versionVo.setFeeName(feeRule.getFeeName());
            versionVo.setVersion(feeRule.getVersion());
            versionVo.setIsActive(feeRule.getIsActive());
            versionVo.setCreateTime(feeRule.getCreateTime());
            versionVo.setCreateBy(feeRule.getCreateBy());
            versionVo.setFeeRule(feeRule);

            // 查询关联的表达式
            LambdaQueryWrapper<TmsFeeRuleExprEntity> exprWrapper = new LambdaQueryWrapper<>();
            exprWrapper.eq(TmsFeeRuleExprEntity::getFeeId, feeRule.getId());
            TmsFeeRuleExprEntity exprEntity = tmsFeeRuleExprMapper.selectOne(exprWrapper);
            versionVo.setFeeRuleExpr(exprEntity);

            // 解析表达式为规则列表
            if (exprEntity != null && StrUtil.isNotBlank(exprEntity.getExpression())) {
                List<TmsSurchargeRuleVo> rules = parseExpressionToRules(exprEntity.getExpression());
                versionVo.setRules(rules);
            }

            versions.add(versionVo);
        }

        return versions;
    }

    /**
     * 获取当前生效版本
     */
    @Override
    public TmsFeeRuleDetailVo getCurrentVersion(Long originalFeeRuleId) {
        if (originalFeeRuleId == null) {
            return null;
        }

        LambdaQueryWrapper<TmsFeeRuleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsFeeRuleEntity::getOriginalFeeRuleId, originalFeeRuleId)
                .eq(TmsFeeRuleEntity::getIsActive, 1);

        TmsFeeRuleEntity currentFeeRule = tmsFeeRuleMapper.selectOne(wrapper);
        if (currentFeeRule == null) {
            return null;
        }

        return getFeeRuleDetailById(currentFeeRule.getId());
    }

    /**
     * 版本对比
     */
    @Override
    public TmsFeeRuleVersionCompareVo compareVersions(Long oldVersionId, Long newVersionId) {
        if (oldVersionId == null || newVersionId == null) {
            return null;
        }

        TmsFeeRuleEntity oldVersion = tmsFeeRuleMapper.selectById(oldVersionId);
        TmsFeeRuleEntity newVersion = tmsFeeRuleMapper.selectById(newVersionId);

        if (oldVersion == null || newVersion == null) {
            return null;
        }

        TmsFeeRuleVersionCompareVo compareVo = new TmsFeeRuleVersionCompareVo();

        // 构建版本信息
        TmsFeeRuleVersionVo oldVersionVo = buildVersionVo(oldVersion);
        TmsFeeRuleVersionVo newVersionVo = buildVersionVo(newVersion);

        compareVo.setOldVersion(oldVersionVo);
        compareVo.setNewVersion(newVersionVo);

        // 对比字段变更
        List<TmsFeeRuleVersionCompareVo.FieldChangeVo> fieldChanges = compareFields(oldVersion, newVersion);
        compareVo.setFieldChanges(fieldChanges);

        return compareVo;
    }

    /**
     * 激活指定版本
     */
    @Override
    public boolean activateVersion(Long versionId) {
        if (versionId == null) {
            return false;
        }

        TmsFeeRuleEntity targetVersion = tmsFeeRuleMapper.selectById(versionId);
        if (targetVersion == null) {
            return false;
        }

        Long originalFeeRuleId = targetVersion.getOriginalFeeRuleId();
        if (originalFeeRuleId == null) {
            return false;
        }

        // 将所有版本设置为非活跃状态
        deactivateAllVersions(originalFeeRuleId);

        // 激活目标版本
        targetVersion.setIsActive(1);
        return tmsFeeRuleMapper.updateById(targetVersion) > 0;
    }

    /**
     * 获取下一个版本号
     */
    private Integer getNextVersionNumber(Long originalFeeRuleId) {
        LambdaQueryWrapper<TmsFeeRuleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsFeeRuleEntity::getOriginalFeeRuleId, originalFeeRuleId)
                .orderByDesc(TmsFeeRuleEntity::getVersion)
                .last("LIMIT 1");

        TmsFeeRuleEntity latestVersion = tmsFeeRuleMapper.selectOne(wrapper);
        if (latestVersion == null) {
            return 1;
        }
        return latestVersion.getVersion() + 1;
    }

    /**
     * 将所有版本设置为非活跃状态
     */
    private void deactivateAllVersions(Long originalFeeRuleId) {
        LambdaQueryWrapper<TmsFeeRuleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsFeeRuleEntity::getOriginalFeeRuleId, originalFeeRuleId);

        List<TmsFeeRuleEntity> allVersions = tmsFeeRuleMapper.selectList(wrapper);
        for (TmsFeeRuleEntity version : allVersions) {
            version.setIsActive(0);
            tmsFeeRuleMapper.updateById(version);
        }
    }


    /**
     * 构建版本VO对象
     */
    private TmsFeeRuleVersionVo buildVersionVo(TmsFeeRuleEntity feeRule) {
        TmsFeeRuleVersionVo versionVo = new TmsFeeRuleVersionVo();
        versionVo.setId(feeRule.getId());
        versionVo.setOriginalFeeRuleId(feeRule.getOriginalFeeRuleId());
        versionVo.setFeeCode(feeRule.getFeeCode());
        versionVo.setFeeName(feeRule.getFeeName());
        versionVo.setVersion(feeRule.getVersion());
        versionVo.setIsActive(feeRule.getIsActive());
        versionVo.setCreateTime(feeRule.getCreateTime());
        versionVo.setCreateBy(feeRule.getCreateBy());
        versionVo.setFeeRule(feeRule);

        // 查询表达式
        LambdaQueryWrapper<TmsFeeRuleExprEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsFeeRuleExprEntity::getFeeId, feeRule.getId());
        TmsFeeRuleExprEntity exprEntity = tmsFeeRuleExprMapper.selectOne(wrapper);
        versionVo.setFeeRuleExpr(exprEntity);

        if (exprEntity != null && StrUtil.isNotBlank(exprEntity.getExpression())) {
            List<TmsSurchargeRuleVo> rules = parseExpressionToRules(exprEntity.getExpression());
            versionVo.setRules(rules);
        }

        return versionVo;
    }

    /**
     * 对比字段变更
     */
    private List<TmsFeeRuleVersionCompareVo.FieldChangeVo> compareFields(TmsFeeRuleEntity oldVersion, TmsFeeRuleEntity newVersion) {
        List<TmsFeeRuleVersionCompareVo.FieldChangeVo> changes = new ArrayList<>();

        // 对比各个字段
        compareField(changes, "feeName", "费用名称", oldVersion.getFeeName(), newVersion.getFeeName());
        compareField(changes, "feeType", "费用类型", oldVersion.getFeeType(), newVersion.getFeeType());
        compareField(changes, "amount", "费用金额", oldVersion.getAmount(), newVersion.getAmount());
        compareField(changes, "calculationMethod", "计算方式", oldVersion.getCalculationMethod(), newVersion.getCalculationMethod());
        compareField(changes, "unit", "计量单位", oldVersion.getUnit(), newVersion.getUnit());
        compareField(changes, "deliveryPercentage", "配送费百分比", oldVersion.getDeliveryPercentage(), newVersion.getDeliveryPercentage());
        compareField(changes, "isValid", "启用状态", oldVersion.getIsValid(), newVersion.getIsValid());

        return changes;
    }

    /**
     * 对比单个字段
     */
    private void compareField(List<TmsFeeRuleVersionCompareVo.FieldChangeVo> changes, String fieldName, String displayName, Object oldValue, Object newValue) {
        if (!java.util.Objects.equals(oldValue, newValue)) {
            TmsFeeRuleVersionCompareVo.FieldChangeVo change = new TmsFeeRuleVersionCompareVo.FieldChangeVo();
            change.setFieldName(fieldName);
            change.setFieldDisplayName(displayName);
            change.setOldValue(oldValue);
            change.setNewValue(newValue);
            change.setChangeType("MODIFIED");
            changes.add(change);
        }
    }

    /**
     * 根据服务商ID查询有效的附加费规则
     *
     * @param providerId 服务商ID
     * @return 有效的附加费规则列表
     */
    @Override
    public List<TmsFeeRuleEntity> getValidFeeRulesByProviderId(Long providerId) {
        if (providerId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<TmsFeeRuleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsFeeRuleEntity::getProviderId, providerId)
                .eq(TmsFeeRuleEntity::getIsValid, 1)
                .eq(TmsFeeRuleEntity::getIsActive, 1) // 只查询当前生效版本
                .orderByAsc(TmsFeeRuleEntity::getFeeType); // 按费用类型排序

        return tmsFeeRuleMapper.selectList(wrapper);
    }
}