error.database.access=系统访问繁忙，请稍后再试。
error.database=系统访问繁忙，请稍后再试。
error.invalid.argument=请求参数不合法：{0}。
error.service.unavailable=服务不可用，请稍后再试：{0}。
error.unauthorized=未授权，请提供有效的认证信息。
error.access.denied=禁止访问，权限不足。
error.method.not.allowed=请求方法不允许。
error.internal.server=服务器内部繁忙，请稍后再试。

error.database.insert.error=操作失败，请检查：{0}.
error.area.insert.error=区域名称重复!
error.warehouse.insert.error=仓库名称重复!
Order.status.is.not.available.for.modification=当前订单状态不支持修改！

tms.exception.handling.error=处理异常结果失败
tms.exception.reporting.content.is.empty=异常上报内容为空
tms.exception.reporting.failed=异常上报失败，请核查
tms.one.ticket.multiple.pieces.not.supported=当前客户仅支持一票一件
tms.no.corresponding.shipping.rule.was.found=未找到对应托盘运费规则
tms.no.valid.base.shipping.template.found=没有找到有效的基础运费模板
tms.no.matching.package.shipping.rules.were.found=未找到匹配的包裹运费规则
tms.origin.or.destination.error=始发地或目的地格式错误,请参考:国家/省/市
tms.Unique.post.names.required=驿站名称不能重复
tms.Unique.post.code.required=驿站代码不能重复
tms.customer.order.already.exists=客户单号已存在
tms.BasicFreightPkgDetail.startWeight.le.endWeight=起始重量必须小于等于截止重量
tms.BasicFreightPkgDetail.weight.interval.repeats=重量区间已存在
tms.imported.template.is.incorrect=模板不正确，请下载最新模板！
tms.app.driver.register.error=司机新增失败：{0}
tms.app.warehouse.register.error=仓库人员新增失败：{0}
tms.app.driver.update.error=司机修改失败
tms.app.warehouse.update.error=仓库人员修改失败
tms.app.warehouse.employee.update.error=仓库人员个人信息修改失败
tms.app.driver.register.poll.code.error=注册码错误:{0}
tms.app.driver.register.phone.repetition=手机号已注册，请重新输入:{0}
tms.app.driver.add.phone.repetition=司机手机号已存在APP终端账号中，无法创建账号:{0}
tms.app.driver.update.not.vehicle.info= 司机未绑定车辆，请联系管理员绑定
tms.app.driver.register.email.and.ID.repetition=邮箱或者驾照号已存在系统，请重新输入:{0}
tms.app.completed.order.not.scan=已完成订单不可扫描：{0}
tms.app.driver.scan.pick.error={0}-不属于本次取货范围
tms.app.driver.scan.pick.repetition={0}-已被取货
tms.app.driver.scan.pick.succeed={0}-已取货成功
tms.app.driver.order.submit.succeed={0}-提交成功！
tms.app.driver.waybill.scan.error={0}-该面单已经扫描取货，不允许再扫描取货!
tms.app.driver.order.not.scan.pickup=存在未扫描的面单：{0}
tms.app.driver.order.scan=订单未扫描：{0}
tms.app.driver.storage.order.no.error=订单号已存在入库记录：{0}
tms.app.driver.warehouse.order.enty=该订单不属于当前仓库：{0}
tms.app.driver.order.not.storage=该订单未入库或已出库：{0}
tms.app.update.scan.status.error=更新扫描状态失败
tms.app.driver.order.not.found=干线任务单不存在：{0}
tms.app.driver.order.update.status.error=更新客户订单状态失败
tms.app.order.not.bound.to.line=该订单不属于干线任务单
tms.app.sub.order.not.exist=扫描订单存在，请重试。
tms.app.main.order.not.exist=主订单不存在
tms.app.warehouse.employee.exist=仓库人员名称或手机号已经存在于系统，请重新输入：{0}
error.lineHaulOrder.notEmpty=勾选的干线任务单不能为空！
error.lineHaulOrder.completedExists=选择的干线任务中有已经完成的干线任务，请检查！
error.unassigned.driver=所选的干线任务中有未指派司机的干线任务,无法进行转单操作，请检查！
tms.app.driver.register.driverNum.repetition=司机号已存在系统，请重新输入：{0}
tms.app.driver.sub.order.not.scan=存在未扫描的订单： {0}




tms.zip.zone.route.number.exists=路线号已存在
tms.zip.zone..Excel.file.processing.success=邮编分区Excel文件处理成功，共处理了 {0} 条
tms.zip.zone..Excel.file.processing.exception=邮编分区Excel文件处理异常：{0}
tms.zip.zone.file.processing.errors=邮编分区导入处理失败，请检查信息：{0}

tms.carrier.add.repetition=该承运商名称已存在：{0}
tms.carrier.register.error={0} 承运商新增失败，请检查


tms.customer.name.exists=客户名称已存在用户管理，无法创建客户端账户：{0}
tms.customer.phone.exists=客户手机号已存在用户管理，无法创建账户：{0}
tms.customer.register.error=客户新增失败，请检查
tms.customer.name.phone.already.exists=客户手机号或用户名已存在系统，请重新输入
tms.customer.update.error=客户修改失败,请检查：{0}
tms.customer.update.password.error=客户密码修改失败，请检查：{0}


tms.app.driver.cage.not.exist= 请检查扫码信息或重新尝试：{0}
tms.app.driver.cage.order.not.exist=订单不存在：{0}
tms.app.driver.order.pickup.proof.not.exist=该订单还未提货：{0}
tms.app.driver.order.delivery.proof.exist={0}-该订单已上传完送货凭证
tms.app.driver.collect.order.not.exist=该司机不存在配送中的订单：{0}
tms.app.report.order.no.empty=报告单号不能为空
tms.app.report.order.not.exist=查不到报告单号对应的任务单：{0}
tms.app.driver.report.order.not.exist=不存在报告单号对应的订单,请检查：{0}
tms.app.driver.cage.order.not.complete=不存在司机未派送完成的任务单
tms.app.driver.has.returned.order=订单已经是配送失败订单：{0}
tms.app.driver.has.returned.warehouse.order=订单已经是待返仓或已返仓状态，不可操作：{0}
tms.app.driver.It.has.been.delivered.three.times=以下订单派送次数已达三次，无法配送，请进行返仓：{0}
tms.app.driver.has.returned.order.not.delivery=以下订单状态不允许再次派送（已完成或已返仓）：{0}
tms.app.driver.has.returned.order.not.cannot.delivery=以下订单状态不允许执行无法配送操作（仅配送失败状态可操作）：{0}
tms.app.driver.has.complete.order=订单已完成：{0}
tms.app.driver.has.pickup.order=请检查订单是否待提货：{0}
tms.app.driver.cage.order.record.exist=订单已存在入笼记录：{0}

tms.user.sms.error=短信发送失败，请稍后重试{0}
tms.user.sms.exception=短信发送异常，请稍后重试{0}

tms.cage.label.cannot.be.deleted = 该标签编码已在干线运输中或者已完成，无法删除:{0}

//路径规划国际化
route_planning.shipment_completed=该运输单已经完成,无需再规划路线
route_planning.shipment_not_found=无此运输单信息!
route_planning.route_already_planned=该运输单已经规划过路线!
route_planning.driver_not_found=有司机信息找不到！
route_planning.vehicle_not_found=司机的车辆信息找不到！
route_planning.token_obtain_failed=获取谷歌地图授权token令牌失败
route_planning.network_fluctuation=网络波动，请稍后重试！
route_planning.invalid_input_parameters=规划输入参数有问题！
route_planning.request_failed=谷歌地图路线规划请求失败
route_planning.success=路线规划成功
route_planning.failure_reason=路线规划失败-原因：{0}
route_planning.no_route_planned=该运输单还没有路线规划
route_planning.no_routes_planned=这些运输单还没有路线规划
route_planning.not_planned_yet=该运输单还没有规划过路线！
route_planning.skip_successful=跳过成功！
route_planning.entrusted_order_not_found=委托单找不到！
route_planning.save_status_successful=保存状态成功！
route_planning.save_failed=保存失败！
route_planning.stop_receiving_orders_successful=停止接单成功！
route_planning.stop_receiving_orders_failed=停止接单失败！
route_planning.shipment_does_not_exist=该运输单不存在！
route_planning.address_range_too_large=路径规划地址范围过大！
route_planning.duplicate_route=批次{0}的路线编号{1}已经进行过路线规划！
batch.not_exists=所选批次不存在！
batch.no_orders=该批次没有订单！
batch.route_number_not_bound=批次订单没有绑定路线编号！
tms.order.batch.create.failed=批量创建失败！

route_planning.duplicate_route_name=第一步的派送批次名称不能重复！
route_planning.missing_delivery_warehouse=有订单的派送起始仓库未匹配上！
route_planning.different_warehouse_areas=请选择同一区域仓库的派送单进行路径规划！
route_planning.empty_delivery_warehouse=有订单的派送起始仓库为空！
route_planning.area_not_exists=订单的派送起始仓库对应区域不存在！
route_planning.site_not_exists=订单的派送起始仓库不存在！
route_planning.driver_no_vehicle=司机未绑定车辆信息！
route_planning.route_planning_failed=路线规划失败！
route_planning.empty_orders=所选订单不能为空！
route_planning.task_order_not_exists=任务单不存在！
route_planning.route_info_not_exists=路线信息不存在！
route_planning.pre_route_plan_not_exists=预路线规划不存在!
order.delivery.location.empty=订单号 {0} 的收货地经纬度为空
warehouse.delivery.location.empty=派送仓库名称 {0} 的经纬度为空
route_planning.over_area_not_exists=覆盖区域不存在！



//委托订单
tms.customer.tracking.number.cannot.be.empty=客户单号不能为空
tms.Driver.ID.is.invalid=司机id不能为空
tms.Driver.not.enabled.or.empty=司机未启用
tms.Driver.not.started=司机还没有开始工作，无法分配
tms.entrusted.order.number.is.invalid=委托订单号无效
tms.order.status.is.not.operable=委托订单状态不可操作
tms.Driver.not.vehicle=司机未绑定车辆，无法生成运输单据
tms.driver.not.app.account.and.cannot.send.messages=司机未绑定app账号，无法发送消息
tms.Number.successfully.allocated.orders=成功分配订单数量：{0}
tms.Abnormal.allocation.of.drivers=司机配置异常，请检查
tms.not.operable.order=没有符合分配司机条件的订单

//客户订单
tms.failed.to.save.customer.order=Failed to save customer order!
tms.box.weight.exceeded=中大件派送，单箱最大重量不得超过30kg。
tms.total.weight.exceeded=最大重量不能超过总重量。
tms.package.weight.exceeded=中大件派送，单包裹的最大重量不能超过30kg。
tms.successfully.created.customer.order=已成功创建客户订单！
tms.failed.to.create.customer.order=创建客户订单失败！
tms.customer.order.number.cannot.be.empty=客户订单号不能为空！
tms.carrier.ID.is.invalid=承运商ID无效！
tms.carrier.is.invalid=承运商无效或为空！
tms.customer.order.number.is.invalid=客户订单号无效！
tms.current.order.status.is.not.operable=当前订单状态不可操作！
tms.no.within.carrier.service.area=承运商服务地区内无
tms.anomalous.allocation.of.carriers=分配承运商异常
tms.order.creation.failed=订单创建失败！
tms.the.order.does.not.exist=订单不存在!
tms.already.has.routing.information=该订单已存在路由信息，不允许删除!
tms.customer.name.cannot.contain.chinese=账户名称不能包含中文
tms.user.name.already.exists=用户名已存在该系统
tms.transportation.order.successful=转单成功！
tms.transportation.order.failed=转单异常！
tms.only.pending.allocation=只有待分配状态才能操作
tms.failed.to.get.delivery.warehouse.id=根据发货地/收货地邮编获取不到揽收或派送仓库【该邮编不在服务范围】
tms.No.order.tracking.info=未查询到订单轨迹信息

//客户
tms.customer.not.exist=客户不存在！

tms.customer.phone.already.exists=客户手机号已存在该系统
tms.failed.to.create.storage.record=创建入库记录失败！
tms.failed.to.create.outbound.record=创建出库记录失败！
tms.successfully.created.record=成功创建记录！
tms.invalid.date.range=开始时间不能大于结束时间

tms.warehouse.positive.number=货仓尺寸必须为正数

tms.vehicle.info.license.plate.repeat=车牌号重复
tms.vehicle.info.contact.phone.repeat=联系电话重复
tms.vehicle.driver.already.assigned=司机 {0} 已绑定车辆，无法重复分配


//站点
tms.site.name.code.exists=站点名称或站点代码已存在
tms.site.address.exists=站点地址已存在

//运输任务单
tms.cargo.info.empty=货物信息为空，无法生成任务单
tms.failed.to.create.transport.task.order=创建运输任务单失败！
tms.site.empty=找不到对应的仓库，三字邮编：{0}
tms.invalid.receive.type=收货类型无效！
tms.customer.id.empty=客户ID不能为空！
tms.get.warehouse.failed=获取仓库ID失败！
tms.delivery.task.created.failed=创建派送任务单失败！
tms.pickup.task.created.failed=创建揽收任务单失败！
tms.not.exist.task.order=找不到符合条件的客户订单！
tms.order.already.assigned=该订单已指派并绑定过司机，不可重复绑定！
tms.order.site.not.same=这些订单的始发地仓库必须是为同一仓库！
tms.receive.type.not.support=订单存在收货方式为“送货到仓”的，不可指派揽收任务，单号：{0}
tms.pickup.task.assigned.success=揽收任务指派成功！{0}
tms.driver.no.vehicle=司机未绑定车辆，无法指派！{0}
tms.driver.rest.failed=司机已休息，请重新选择司机！{0}
tms.tracking.number.cannot.be.empty=跟踪单号不能为空
tms.transfer.success=订单转单成功！
tms.no.allowed.transfer=订单未完成指派，不允许转单
tms.order.status.already.changed=仅待揽收状态的订单可分配司机 {0}

//干线
tms.line.task.order.successful=干线任务单创建成功！
tms.line.task.order.failed=干线任务单创建失败！
tms.not.exist.line.task.order=不存在待指派的干线任务单
tms.line.task.order.not.exist=干线任务单不存在
tms.line.task.order.update.failed=干线任务单更新失败
tms.line.task.order.update.success=干线任务单更新成功
tms.line.task.not.greater.than.zero=数量、体积、重量不能小于0
tms.line.task.only.pending.allocation=只有待提货状态才能操作
tms.origin.warehouse.equals.dest.warehouse=始发地仓库不能与目的地仓库相同
tms.order.some.not.bindable=部分订单不能绑定
tms.driver.not.exist=司机不存在！
tms.vehicle.not.exist=车辆不存在！
tms.app.driver.label.already.scanned=标签已扫描！
tms.app.driver.label.not.all.scanned=该批次还有标签未扫描！
tms.app.driver.label.not.exist=该标签不存在！
tms.app.driver.start.site.not.exist=标签的始发仓库不存在！
tms.app.driver.label.task.in.progress=该标签对应的任务正在运输或已经完成状态，不允许再扫码入笼！

//客户端
account.not.opened=您的账号并未开户，请联系管理员开户！
email.format.invalid=邮箱格式不正确！
mobile.format.invalid=手机号格式不正确！
verification.code.invalid=验证码错误或已过期，请重新获取！
sms.verification.code.invalid=短信验证码错误或已过期，请重新获取！
real.name.authentication.required=您暂未完成实名认证，请先完成实名认证，您当前状态不可操作！
selected.order.not.null=所选订单不能为空！
update.mobile.failed=修改手机号失败！
update.password.failed=修改密码失败！
update.email.failed=修改邮箱失败！
account.not.approved=该账号还未审核，请联系管理员审核后再操作！
phone.not.registered=该手机号并未在系统注册！
phone.already.registered=该手机号已被其他用户注册了，无法重复注册！
phone.already.bound=该手机号已绑定其他账号，无法修改！
email.already.registered=该邮箱已经被其他人绑定了，无法修改！
old.password.incorrect=原密码不对，无法修改！
register.failed=注册失败！

//异常
tms.exception.taskOrder.already.uploaded=该任务单已经上传过异常了！

//标签管理
tms.label.code.clash=标签编码生成冲突，请稍后再试

verification.code.has.expired.please.obtain.the.verification.code.again=图形验证码已过期，请重新获取验证码
verification.code.is.incorrect=图形验证码不正确

tms.app.driver.cage.order.not.mismatching=笼车信息与订单信息地点路线不一致：{0}

//格口管理
tms.app.driver.grid.order.not.mismatching=该三字邮编 [{0}] 不在覆盖区域范围内
tms.area.not.match.postal.code=不存在该三字邮编对应的覆盖区域
tms.sorting.successful=分拣成功！
tms.no.sorting.order=待揽收和已完成订单不可分拣
tms.no.order.info=未查询到相关订单信息
tms.collection.task.not.completed=以下订单存在未完成的揽收任务,如下：{0}
tms.invalid.order=存在无效的订单：{0}
tms.tracking.number.not.compliant=该跟踪单号：{0} 不合规，应为面单单号
tms.grid.only.one.routeNumber=一个路线号仅能绑定一个格口！
tms.grid.code.or.name.exists=格口编码或名称已存在,请检查
tms.no.sorting.grid=该仓库：{0} 未维护格口，请先维护格口
tms.batch.no.exist=该单号已加入批次，无法重复分拣！单号：{0}

//地址簿
tms.duplicate.address=该联系人地址已存在

//订单批次
tms.orderbatch.created.successful=批次创建成功
tms.orderbatch.select.successful=批次选择成功
tms.not.exist.order.batchno=订单批次号不存在
tms.orderbatch.updated.failed=批次信息更新失败
tms.null.area.order.batchno=所选订单的目的地派送区域数据为空
tms.batchno.repeat=批次号重复


//轨迹节点维护
tms.track.node.context.repetition=节点状态码或者触发节点重复

//app揽收订单
tms.collection.batch.pickup.error=以下订单不符合上传条件（未全部扫描或状态不是“待揽收”）：{0}

//官网轨迹验证码
tms.captcha.has.been.generated=验证码已生成：{0}
tms.captcha.not.null=订单号和验证码不能为空
tms.captcha.has.expired=验证码已过期，请重新获取：{0}
tms.captcha.error=验证码错误：{0}
tms.captcha.verified=验证码已通过：{0}

tms.store.name.phone.already.exists=该联系人或联系方式已存在，请重新输入
tms.store.delete.failed=删除商店失败
tms.store.id.empty=门店不能为空
tms.store.employee.create.fail=创建员工失败 {0}
tms.store.code.already.exists=门店编码已存在，请重新输入 {0}
tms.store.customer.code.already.exists=客户代码已存在，请重新输入 {0}
tms.order.has.been.written.off=订单已验证，无法取消
tms.order.cancel.failed=订单取消失败 {0}
tms.store.order.not.exist=订单不存在
tms.store.order.lack.amount=订单缺少运费金额
tms.store.order.pkg.comp.amount.invalid=赔付金额不能为空并且需大于0
tms.store.order.pkg.beyond.amount=赔付金额不能超过运费金额
tms.store.order.pkg.no.balance.account=客户余额账户不存在
tms.store.order.pkg.beyond.balance=客户余额不足
tms.store.order.pkg.compensate.failed=客户余额赔付失败
tms.store.order.pkg.pay.save.failed=赔付记录保存失败
tms.store.order.status.invalid=当前订单状态不允许进行赔付
tms.store.order.already.writeoff=此订单已核销，请勿重复操作
tms.store.phone.already.exists=该联系方式已存在，请重新输入

//web端司机上传揽收POD
tms.web.collent.upload.error=该订单还未进行提货，请重试：{0}


//返仓自提
error.warehouse.notExist=所选仓位不存在！
error.order.alreadyScanned=订单【{0}】已存在上架扫描记录！
error.order.noNeedScan=订单【{0}】无需返仓上架扫描！
error.order.notFound=没有找到对应的订单!
error.submit.fail=提交失败！
error.orderNo.format=订单号格式错误,请检查！
error.order.alreadyOnShelf=该包裹订单已存在上架记录！
error.order.notInAppointment=该单不属于预约待取货状态，不可进行出仓处理！

//导入提示语
order.upload.valid.file=请上传有效的 Excel 文件！
order.upload.empty.data=Excel文件中数据为空

tms.ServiceRegionDetail.note.Excel.file.processing.success=分区邮编配置明细Excel文件处理成功，共处理了 {0} 条
tms.ServiceRegionDetail.file.processing.errors=分区邮编配置明细导入处理失败，请检查信息：{0}
tms.ServiceRegionDetail.Excel.file.processing.exception=分区邮编配置明细Excel文件处理异常：{0}

tms.ServiceQuotePrice.note.Excel.file.processing.success=价格配置明细Excel文件处理成功，共处理了 {0} 条
tms.ServiceQuotePrice.file.processing.errors=价格配置明细导入处理失败，请检查信息：{0}
tms.ServiceQuotePrice.Excel.file.processing.exception=价格配置明细Excel文件处理异常：{0}


tms.customer.reservation.pickup.address.errors = 请您填写正确的加拿大地址或者邮政编码
tms.customer.reservation.pickup.account.errors = 客户账号信息异常,请联系开发人员检查!



//服务商管理
tms.serviceProvider.name.exists=服务商名称或代码不能重复：{0}

//-服务商邮编
tms.serviceProvider.postalCode.exists=分区名称或代码不能重复：{0}
tms.ServiceRegionDetail.file.too.many.rows=上传的文件行数超出限制，最多允许:{0}条记录

//-新增服务商邮编配置子表
tms.serviceProvider.postalCode.dest.exists=邮编分区目的地邮编起始-结束区间不能重复:{0}
tms.serviceProvider.postalCode.shipper.exists=邮编分区发件人邮编起始-结束区间不能重复:{0}

//--------------------------------财务模块
//-报价
tms.serviceQuote.name.exists=报价名称或报价代码不能重复:{0}
tms.serviceQuote.channel.name.exists=该服务商-渠道已存在对应的报价规则
tms.serviceQuote.keda.fenqu.exists=该可达分区已存在对应的报价规则

# 快递业务模块国际-en
# 普通公用
tms.store.params.exception=参数异常
tms.store.code.invalid=验证码失效
tms.store.phone.illegal=手机号格式错误
tms.store.phone.exists=手机号已存在
# 订单
tms.store.order.not.exists=订单不存在
tms.store.order.goods.not.exists=货物信息不能为空
tms.store.order.goods.check.not.empty=货物长度/宽度/高度/重量不能为空
tms.store.order.provider.not.exists=末端派送服务商不能为空
tms.store.order.freight.amount.exception=运费异常
tms.store.order.total.weight.kg.maxLimit=重量不能超过68kg
tms.store.order.totalWeight.kg.maxLimit=总重量不能超过68kg
tms.store.order.total.weight.lb.maxLimit=重量不能超过149.91磅
tms.store.order.write.off.finished=订单已核销,不支持操作
tms.store.order.print.script.failed=打印凭证失败
tms.store.order.cancel.order.status.failed=订单状态异常,非待发货且已核销,不支持取消订单
tms.store.order.not.awaiting.shipment.write.off.failed=订单状态非待发货,不支持核销
tms.store.order.write.off.finished.failed=订单已核销,不支持操作核销
tms.store.order.write.off.freight.amount.exception=线下支付,运费金额需大于等于零
tms.store.order.write.off.pay.type.exception=支付方式异常
# 客户
tms.store.customer.not.exists=客户不存在
tms.store.customer.email.not.legal=邮箱不合法
tms.store.customer.email.exists=邮箱已存在
tms.store.customer.email.not.match=邮箱不匹配
tms.store.customer.email.modify.failed=修改邮箱失败
tms.store.customer.phone.modify.failed=手机号修改失败
tms.store.customer.password.check.failed=两次输入密码不一致
tms.store.customer.reset.password.failed=重置密码失败
tms.store.nb.customer.not.exists=NB系统快递业务客户不存在
# 余额
tms.store.balance.customer.not.exists=客户余额不存在
tms.store.balance.customer.not.enough=客户余额不足,请前往充值
tms.store.balance.amount.change.failed=余额变更失败
tms.store.balance.insufficient=客户余额不足
# 服务商
tms.store.provider.not.exists=无启用服务商,不支持询价
tms.store.provider.box.not.exists=盲盒未配置,不支持询价
tms.store.provider.default.dispatch.not.supported=无可派送的推荐服务商,请核查地址和货物信息
tms.store.provider.other.dispatch.not.supported=无可派送的其它服务商,请核查地址和货物信息
tms.store.provider.profit.bind.box.not.exists=仅用于盲盒服务商利润率配置不存在,不支持询价
tms.store.provider.profit.not.exists=服务商利润率配置不存在,不支持询价
# 推送订单
tms.store.push.order.nb.exception=推送NB系统,推送失败
tms.store.push.order.package.exception=推送小包系统,推送失败
# 信用卡
tms.store.credit.card.exists=卡号已存在
tms.store.credit.card.not.exists=信用卡信息不存在
# 优惠码
tms.store.promotion.code.invalid=优惠码无效,请检查后重新输入!
tms.store.promotion.code.unusable=优惠码已无可用次数,请更换其它优惠码!
tms.store.promotion.code.bind.provider.error=当前选择的服务商不符合优惠码绑定的服务商,{0},请更换其它优惠码!
tms.store.promotion.code.bind.provider.not.exists=优惠码绑定的服务商不存在!
tms.store.promotion.code.threshold.enough=当前订单金额未达到优惠码使用要求,{0} CAD,请更换其它优惠码!
# 推广
tms.store.promotion.promoter.exists=推广人或者联系方式已存在
tms.store.promotion.settle.amount.invalid=结算金额必须大于0
tms.store.promotion.commission.data.not.found=未找到对应的佣金数据
tms.store.promotion.settle.amount.exceed=结算金额不能大于未结算金额
tms.store.promotion.data.validation.failed=数据校验失败，请联系管理员
tms.store.promotion.promoter.order.exists=该推广人存在推广记录，不允许删除
tms.store.promotion.code.used=优惠码已经被使用，不允许删除
tms.store.promotion.code.valid.time.less=优惠码有效期不能少于1天
tms.store.promotion.code.format.invalid=推广码只能由数字和字母构成！
tms.store.promotion.code.length.invalid=推广码长度只能为10位！
tms.store.promotion.code.exist=推广码已存在
//--------------------------------客户渠道报价
tms.customerChannelQuote.name.exists=该客户-该渠道报价已存在

# 服务商利润配置
tms.store.profit.config.data.empty=配置对象不能为空
tms.store.profit.config.detail.data.empty=配置重量明细不能为空
tms.store.profit.config.add.error=保存失败：主配置插入异常
tms.store.profit.config.detail.add.error=保存失败：重量明细配置插入异常

tms.store.profit.config.update.data.empty=配置对象或ID不能为空
tms.store.profit.config.update.error=更新失败：主配置更新异常
tms.store.profit.config.detail.update.error=更新失败：重量明细配置更新异常
tms.store.profit.config.detail.update.add.error=更新失败：重量明细配置新增异常

tms.store.profit.config.detail.weight.range.overlap=重量区间重叠或重复: {0}
tms.store.profit.config.detail.weight.empty=重量区间不能为空
tms.store.profit.config.detail.weight.start.end.ge=起始重量不能大于结束重量：{0}

tms.store.profit.config.unique=服务商已存在启用状态的{0}利润配置，不能重复添加


# finance
tms.store.finance.order.number=订单号
tms.store.finance.tracking.number=推送外部单号
tms.store.finance.price=运费总额
tms.store.finance.pay.type=支付方式
tms.store.finance.order.create.time=订单创建日期
tms.store.finance.name=客户名称
tms.store.finance.level=客户等级
tms.store.finance.type=客户类型

# channel
tms.channel.channel.code=渠道代码
tms.channel.channel.name=渠道名称
tms.channel.channel.type=渠道类型
tms.channel.goods.type=货物属性
tms.channel.time.lineless=时效
tms.channel.volume.weight=材积除
tms.channel.billing.node=计费节点
tms.channel.service.provider=服务商
tms.channel.reachable.area=可选分区
tms.channel.unreachable.area=不可达分区
